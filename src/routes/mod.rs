pub mod auth;
pub mod plants;
pub mod plots;
pub mod seeds;
pub mod seasons;
pub mod season_plans;
pub mod admin;
pub mod notifications;
pub mod property;
pub mod households;
pub mod profile;
pub mod wishlist;

use actix_web::{web, HttpResponse, Result, middleware::ErrorHandlerResponse, dev::ServiceResponse};
use actix_session::Session;
use crate::utils::templates::{render_template, render_template_with_context};

pub mod wizard;

async fn test_route() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().body("Test route works!"))
}

async fn test_template(session: Session) -> Result<HttpResponse> {
    let mut ctx = tera::Context::new();
    render_template_with_context("test.html", &mut ctx, &session)
}

pub fn init(cfg: &mut web::ServiceConfig) {
    // Register index route first
    cfg.route("/", web::get().to(index));
    cfg.route("/test", web::get().to(test_route));
    cfg.route("/test-template", web::get().to(test_template));

    plants::init(cfg);
    wizard::init(cfg);
    plots::init(cfg);
    seeds::init(cfg);
    seasons::init(cfg);
    season_plans::init(cfg);
    notifications::init(cfg);
    admin::init(cfg);
    auth::init(cfg);
    property::init(cfg);
    households::init(cfg);
    profile::init(cfg);
    wishlist::init(cfg);

    // Add error handlers
    cfg.default_service(web::route().to(not_found_handler));
}


async fn index(session: Session) -> Result<HttpResponse> {
    let mut ctx = tera::Context::new();
    render_template_with_context("index.html", &mut ctx, &session)
}
async fn not_found_handler(session: Session) -> Result<HttpResponse> {
    let mut ctx = tera::Context::new();
    match render_template_with_context("errors/404.html", &mut ctx, &session) {
        Ok(response) => Ok(HttpResponse::NotFound().content_type("text/html").body(response.body())),
        Err(_) => Ok(HttpResponse::NotFound().body("404 Page not found"))
    }
}