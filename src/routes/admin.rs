use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};

use crate::models::user::User;
use crate::models::plant::Plant;
use crate::schema::{users, plants};
use crate::utils::templates::{render_template, render_template_with_context};
use crate::utils::auth::is_authenticated;
use crate::utils::herba_scraper::scrape_plant_info;
use crate::DbPool;

pub async fn admin_dashboard(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if is_admin {
        let mut conn = pool.get().expect("Couldn't get DB connection from pool");

        // Get user statistics
        let total_users = users::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
        let total_plants = plants::table.count().get_result::<i64>(&mut conn).unwrap_or(0);

        let mut ctx = tera::Context::new();
        ctx.insert("total_users", &total_users);
        ctx.insert("total_plants", &total_plants);
        ctx.insert("user_role", &user_role);

        render_template_with_context("admin/dashboard.html", &mut ctx, &session)
    } else {
        Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."))
    }
}

// Add a simple admin index route
pub async fn admin_index(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse, actix_web::Error> {
    admin_dashboard(session, pool).await
}

// User management
pub async fn list_users(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let all_users = users::table
        .load::<User>(&mut conn)
        .expect("Error loading users");

    let mut ctx = tera::Context::new();
    ctx.insert("users", &all_users);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/users/list.html", &mut ctx, &session)
}

#[derive(Deserialize)]
pub struct UpdateUserRoleForm {
    pub user_id: i32,
    pub role: String,
}

#[derive(Deserialize)]
pub struct CreateUserForm {
    pub username: String,
    pub email: String,
    pub password: String,
    pub role: String,
}

#[derive(Deserialize)]
pub struct EditUserForm {
    pub username: String,
    pub email: String,
    pub role: String,
    pub password: Option<String>, // Optional password update
}

pub async fn update_user_role(
    session: Session,
    form: web::Form<UpdateUserRoleForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_superadmin = user_role == "superadmin";

    if !is_superadmin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: Only superadmin can change user roles."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    diesel::update(users::table.find(form.user_id))
        .set(users::role.eq(&form.role))
        .execute(&mut conn)
        .expect("Error updating user role");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/admin/users"))
        .finish())
}

// Create new user
pub async fn create_user_form(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut ctx = tera::Context::new();
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/users/create.html", &mut ctx, &session)
}

pub async fn create_user(
    session: Session,
    form: web::Form<CreateUserForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this feature."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Hash the password
    let password_hash = bcrypt::hash(&form.password, bcrypt::DEFAULT_COST)
        .map_err(|_| actix_web::error::ErrorInternalServerError("Failed to hash password"))?;

    // Create new user
    use crate::schema::users::dsl::*;
    let new_user = crate::models::user::NewUser {
        username: &form.username,
        email: &form.email,
        password_hash: &password_hash,
        role: &form.role,
    };

    match diesel::insert_into(users)
        .values(&new_user)
        .execute(&mut conn)
    {
        Ok(_) => Ok(HttpResponse::Found()
            .append_header(("Location", "/admin/users"))
            .finish()),
        Err(e) => {
            eprintln!("Error creating user: {}", e);
            Ok(HttpResponse::InternalServerError()
                .body("Failed to create user"))
        }
    }
}

// Edit user
pub async fn edit_user_form(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let user_id = path.into_inner();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let user = users::table
        .find(user_id)
        .first::<User>(&mut conn)
        .map_err(|_| actix_web::error::ErrorNotFound("User not found"))?;

    let mut ctx = tera::Context::new();
    ctx.insert("user", &user);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/users/edit.html", &mut ctx, &session)
}

pub async fn update_user(
    session: Session,
    path: web::Path<i32>,
    form: web::Form<EditUserForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this feature."));
    }

    let user_id = path.into_inner();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    use crate::schema::users::dsl::*;

    // Update user fields
    let mut update_query = diesel::update(users.find(user_id));

    if let Some(new_password) = &form.password {
        if !new_password.is_empty() {
            let password_hash = bcrypt::hash(new_password, bcrypt::DEFAULT_COST)
                .map_err(|_| actix_web::error::ErrorInternalServerError("Failed to hash password"))?;

            update_query = update_query.set((
                username.eq(&form.username),
                email.eq(&form.email),
                role.eq(&form.role),
                password_hash.eq(password_hash),
            ));
        } else {
            update_query = update_query.set((
                username.eq(&form.username),
                email.eq(&form.email),
                role.eq(&form.role),
            ));
        }
    } else {
        update_query = update_query.set((
            username.eq(&form.username),
            email.eq(&form.email),
            role.eq(&form.role),
        ));
    }

    match update_query.execute(&mut conn) {
        Ok(_) => Ok(HttpResponse::Found()
            .append_header(("Location", "/admin/users"))
            .finish()),
        Err(e) => {
            eprintln!("Error updating user: {}", e);
            Ok(HttpResponse::InternalServerError()
                .body("Failed to update user"))
        }
    }
}

// Delete user
pub async fn delete_user(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_superadmin = user_role == "superadmin";

    if !is_superadmin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: Only superadmin can delete users."));
    }

    let user_id = path.into_inner();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    use crate::schema::users::dsl::*;
    match diesel::delete(users.find(user_id)).execute(&mut conn) {
        Ok(_) => Ok(HttpResponse::Found()
            .append_header(("Location", "/admin/users"))
            .finish()),
        Err(e) => {
            eprintln!("Error deleting user: {}", e);
            Ok(HttpResponse::InternalServerError()
                .body("Failed to delete user"))
        }
    }
}

// HerbaDB management
pub async fn manage_herba_db(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin" || user_role == "moderator";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let all_plants = plants::table
        .load::<Plant>(&mut conn)
        .expect("Error loading plants");

    let mut ctx = tera::Context::new();
    ctx.insert("plants", &all_plants);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/herba_database.html", &mut ctx, &session)
}

#[derive(Deserialize)]
pub struct ScrapeForm {
    pub plant_name: String,
}

pub async fn scrape_herba_info(
    session: Session,
    form: web::Form<ScrapeForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin" || user_role == "moderator";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this feature."));
    }

    // Scrape plant information
    match scrape_plant_info(&form.plant_name).await {
        Ok(plant_info) => {
            // For now, just return the scraped information as JSON
            // In a full implementation, you'd save this to the database
            let response_html = format!(
                r#"
                <div class="max-w-4xl mx-auto p-6">
                    <h1 class="text-2xl font-bold mb-4">Scraped Plant Information: {}</h1>
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h2 class="text-xl font-semibold mb-2">Basic Information</h2>
                        <p><strong>Name:</strong> {}</p>
                        <p><strong>Scientific Name:</strong> {}</p>
                        <p><strong>Common Names:</strong> {}</p>
                        <p><strong>Description:</strong> {}</p>

                        <h2 class="text-xl font-semibold mb-2 mt-6">Growing Information</h2>
                        <p><strong>Plant Type:</strong> {}</p>
                        <p><strong>Watering Needs:</strong> {}</p>
                        <p><strong>Light Requirements:</strong> {}</p>
                        <p><strong>Soil Type:</strong> {}</p>
                        <p><strong>Hardiness Zone:</strong> {}</p>

                        <h2 class="text-xl font-semibold mb-2 mt-6">Special Features</h2>
                        <p>{}</p>

                        <div class="mt-6">
                            <a href="/admin/herba-db" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                                Back to HerbaDB
                            </a>
                        </div>
                    </div>
                </div>
                "#,
                plant_info.name,
                plant_info.name,
                plant_info.scientific_name.unwrap_or_default(),
                plant_info.common_names.join(", "),
                plant_info.description.unwrap_or_default(),
                plant_info.plant_type.unwrap_or_default(),
                plant_info.watering_needs.unwrap_or_default(),
                plant_info.light_requirements.unwrap_or_default(),
                plant_info.soil_type.unwrap_or_default(),
                plant_info.hardiness_zone.unwrap_or_default(),
                plant_info.special_features.join(", ")
            );

            Ok(HttpResponse::Ok()
                .content_type("text/html")
                .body(response_html))
        }
        Err(e) => {
            Ok(HttpResponse::InternalServerError()
                .content_type("text/html")
                .body(format!("Error scraping plant information: {}", e)))
        }
    }
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/admin")
            .route("", web::get().to(admin_index))
            .route("/dashboard", web::get().to(admin_dashboard))
            .route("/users", web::get().to(list_users))
            .route("/users/create", web::get().to(create_user_form))
            .route("/users/create", web::post().to(create_user))
            .route("/users/{id}/edit", web::get().to(edit_user_form))
            .route("/users/{id}/edit", web::post().to(update_user))
            .route("/users/{id}/delete", web::post().to(delete_user))
            .route("/users/update-role", web::post().to(update_user_role))
            .route("/herba-db", web::get().to(manage_herba_db))
            .route("/herba-db/scrape", web::post().to(scrape_herba_info)),
    );
}
