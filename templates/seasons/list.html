{% extends "base.html" %}
{% block title %}Seasons{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">Seasons</h1>
        <a href="/seasons/new" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded transition-colors">Add New Season</a>
    </div>

    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md overflow-hidden">
        <table class="min-w-full">
            <thead class="bg-sage-50 dark:bg-sage-900">
                <tr>
                    <th class="py-3 px-4 text-left text-xs font-medium text-sage-700 dark:text-sage-300 uppercase tracking-wider">ID</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-sage-700 dark:text-sage-300 uppercase tracking-wider">Name</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-sage-700 dark:text-sage-300 uppercase tracking-wider">Start Date</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-sage-700 dark:text-sage-300 uppercase tracking-wider">End Date</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-sage-700 dark:text-sage-300 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-sage-200 dark:divide-sage-700">
                {% for season in seasons %}
                <tr class="hover:bg-sage-50 dark:hover:bg-sage-700 transition-colors">
                    <td class="py-4 px-4 text-sm text-sage-900 dark:text-sage-100">{{ season.id }}</td>
                    <td class="py-4 px-4 text-sm font-medium text-sage-900 dark:text-sage-100">{{ season.name }}</td>
                    <td class="py-4 px-4 text-sm text-sage-700 dark:text-sage-300">{{ season.start_date }}</td>
                    <td class="py-4 px-4 text-sm text-sage-700 dark:text-sage-300">{{ season.end_date }}</td>
                    <td class="py-4 px-4 text-sm">
                        <a href="/seasons/{{ season.id }}/plan" class="text-sage-600 hover:text-sage-900 dark:text-sage-400 dark:hover:text-sage-200 font-medium">View Plan</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        {% if seasons|length == 0 %}
        <div class="py-8 text-center">
            <p class="text-sage-500 dark:text-sage-400">No seasons found. Create your first season to get started!</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
