{% extends "base.html" %}
{% block title %}Server Error{% endblock %}
{% block content %}
<div class="min-h-screen flex items-center justify-center bg-sage-50 dark:bg-sage-950">
    <div class="max-w-md w-full bg-white dark:bg-sage-800 rounded-lg shadow-lg p-8 text-center">
        <div class="mb-6">
            <svg class="mx-auto h-24 w-24 text-red-400 dark:text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
        </div>
        
        <h1 class="text-4xl font-bold text-sage-900 dark:text-sage-100 mb-4">500</h1>
        <h2 class="text-xl font-semibold text-sage-700 dark:text-sage-200 mb-4">Server Error</h2>
        
        <p class="text-sage-600 dark:text-sage-300 mb-6">
            Something went wrong on our end. Our gardeners are working to fix this issue.
            Please try again in a few moments.
        </p>
        
        <div class="space-y-3">
            <a href="/" class="block w-full bg-sage-600 hover:bg-sage-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                Return to Garden
            </a>
            
            <button onclick="window.location.reload()" class="block w-full bg-sage-100 hover:bg-sage-200 dark:bg-sage-700 dark:hover:bg-sage-600 text-sage-700 dark:text-sage-200 font-medium py-2 px-4 rounded-lg transition-colors">
                Try Again
            </button>
        </div>
        
        <div class="mt-6 text-sm text-sage-500 dark:text-sage-400">
            <p>Error ID: {{ error_id | default("Unknown") }}</p>
        </div>
    </div>
</div>
{% endblock %}
