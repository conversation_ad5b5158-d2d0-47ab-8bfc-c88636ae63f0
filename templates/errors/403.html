{% extends "base.html" %}
{% block title %}Access Forbidden{% endblock %}
{% block content %}
<div class="min-h-screen flex items-center justify-center bg-sage-50 dark:bg-sage-950">
    <div class="max-w-md w-full bg-white dark:bg-sage-800 rounded-lg shadow-lg p-8 text-center">
        <div class="mb-6">
            <svg class="mx-auto h-24 w-24 text-amber-400 dark:text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
            </svg>
        </div>
        
        <h1 class="text-4xl font-bold text-sage-900 dark:text-sage-100 mb-4">403</h1>
        <h2 class="text-xl font-semibold text-sage-700 dark:text-sage-200 mb-4">Access Forbidden</h2>
        
        <p class="text-sage-600 dark:text-sage-300 mb-6">
            You don't have permission to access this area of the garden. 
            This section may require special privileges or be restricted to certain user roles.
        </p>
        
        <div class="space-y-3">
            <a href="/" class="block w-full bg-sage-600 hover:bg-sage-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                Return to Garden
            </a>
            
            <a href="/auth/login" class="block w-full bg-sage-100 hover:bg-sage-200 dark:bg-sage-700 dark:hover:bg-sage-600 text-sage-700 dark:text-sage-200 font-medium py-2 px-4 rounded-lg transition-colors">
                Login
            </a>
        </div>
        
        <div class="mt-6 text-sm text-sage-500 dark:text-sage-400">
            <p>If you believe you should have access, please contact an administrator.</p>
        </div>
    </div>
</div>
{% endblock %}
