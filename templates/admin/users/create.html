{% extends "base.html" %}
{% block title %}Create User - Admin{% endblock %}
{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">Create New User</h1>
        <a href="/admin/users" class="bg-sage-500 hover:bg-sage-600 text-white px-4 py-2 rounded transition-colors">
            Back to Users
        </a>
    </div>

    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
        <form method="post" action="/admin/users/create" class="space-y-6">
            <div>
                <label for="username" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">
                    Username
                </label>
                <input type="text" id="username" name="username" required
                    class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
            </div>

            <div>
                <label for="email" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">
                    Email
                </label>
                <input type="email" id="email" name="email" required
                    class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">
                    Password
                </label>
                <input type="password" id="password" name="password" required
                    class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
            </div>

            <div>
                <label for="role" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">
                    Role
                </label>
                <select id="role" name="role" required
                    class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
                    <option value="viewer">Viewer</option>
                    <option value="commenter">Commenter</option>
                    <option value="moderator">Moderator</option>
                    {% if user_role == "superadmin" %}
                    <option value="admin">Admin</option>
                    <option value="superadmin">Superadmin</option>
                    {% endif %}
                </select>
            </div>

            <div class="flex justify-end space-x-4">
                <a href="/admin/users" class="bg-sage-300 hover:bg-sage-400 text-sage-700 px-4 py-2 rounded transition-colors">
                    Cancel
                </a>
                <button type="submit" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded transition-colors">
                    Create User
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
