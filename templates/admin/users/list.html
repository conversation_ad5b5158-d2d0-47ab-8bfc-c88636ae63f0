{% extends "base.html" %}
{% block title %}User Management{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">User Management</h1>
        <div class="space-x-2">
            <a href="/admin/users/create" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded transition-colors">
                Create New User
            </a>
            <a href="/admin" class="bg-sage-500 hover:bg-sage-600 text-white px-4 py-2 rounded transition-colors">
                Back to Dashboard
            </a>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold">All Users ({{ users|length }})</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            User
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Role
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {% for user in users %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-green-600 flex items-center justify-center text-white font-medium">
                                        <!-- Plant-like avatar -->
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2C8.13 2 5 5.13 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1s1-.45 1-1v-2.26c.31.16.65.26 1 .26s.69-.1 1-.26V17c0 .55.45 1 1 1s1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.87-3.13-7-7-7z"/>
                                            <circle cx="10" cy="8" r="1"/>
                                            <circle cx="14" cy="8" r="1"/>
                                            <path d="M10 11c0 1.1.9 2 2 2s2-.9 2-2"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ user.username }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        ID: {{ user.id }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                {% if user.role == 'superadmin' %}bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200
                                {% elif user.role == 'admin' %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                {% elif user.role == 'moderator' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                {% else %}bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200{% endif %}">
                                {{ user.role | title }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                {% if user.id != user_context.user_id %}
                                <a href="/admin/users/{{ user.id }}/edit" class="text-sage-600 hover:text-sage-900 dark:text-sage-400 dark:hover:text-sage-200">
                                    Edit
                                </a>
                                {% if user_role == "superadmin" %}
                                <form method="post" action="/admin/users/{{ user.id }}/delete" class="inline" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                    <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-200">
                                        Delete
                                    </button>
                                </form>
                                {% endif %}
                                {% elif user.id == user_context.user_id %}
                                <span class="text-blue-600 dark:text-blue-400 font-medium">You</span>
                                {% endif %}
                            </div>

                            {% if user_role == "superadmin" and user.id != user_context.user_id %}
                            <form method="post" action="/admin/users/update-role" class="inline mt-2">
                                <select name="role" class="text-xs border rounded px-2 py-1 dark:bg-sage-700 dark:border-sage-600 dark:text-white" onchange="this.form.submit()">
                                    <option value="viewer" {% if user.role == "viewer" %}selected{% endif %}>Viewer</option>
                                    <option value="commenter" {% if user.role == "commenter" %}selected{% endif %}>Commenter</option>
                                    <option value="moderator" {% if user.role == "moderator" %}selected{% endif %}>Moderator</option>
                                    <option value="admin" {% if user.role == "admin" %}selected{% endif %}>Admin</option>
                                    <option value="superadmin" {% if user.role == "superadmin" %}selected{% endif %}>Superadmin</option>
                                </select>
                                <input type="hidden" name="user_id" value="{{ user.id }}">
                            </form>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if users|length == 0 %}
        <div class="px-6 py-8 text-center">
            <p class="text-gray-500 dark:text-gray-400">No users found.</p>
        </div>
        {% endif %}
    </div>

    <!-- Role Information -->
    <div class="mt-6 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">Role Permissions</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div>
                <h4 class="font-medium text-blue-800 dark:text-blue-200">Superadmin</h4>
                <p class="text-blue-700 dark:text-blue-300">Full system access, can manage all users and settings</p>
            </div>
            <div>
                <h4 class="font-medium text-blue-800 dark:text-blue-200">Admin</h4>
                <p class="text-blue-700 dark:text-blue-300">Can manage users, HerbaDB, and system settings</p>
            </div>
            <div>
                <h4 class="font-medium text-blue-800 dark:text-blue-200">Moderator</h4>
                <p class="text-blue-700 dark:text-blue-300">Can manage HerbaDB and moderate content</p>
            </div>
            <div>
                <h4 class="font-medium text-blue-800 dark:text-blue-200">User</h4>
                <p class="text-blue-700 dark:text-blue-300">Standard user with access to personal features</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
