<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Garden Planner{% endblock %}</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/wizard.css">
    <script src="https://unpkg.com/htmx.org@1.9.2"></script>
    <script src="/static/js/scripts.js" defer></script>
    {% block head %}{% endblock %}
</head>
<body class="bg-sage-50 dark:bg-sage-950 text-sage-900 dark:text-sage-100 transition-colors duration-200">
    <!-- Navigation -->
    <nav class="bg-sage-700 dark:bg-sage-800 text-white shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="text-xl font-bold text-white hover:text-sage-200 transition-colors">
                        🌱 Garden Planner
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-6">
                    {% if user_context.is_authenticated %}
                        <!-- Main Navigation Links -->
                        <div class="flex items-center space-x-4">
                            <a href="/" class="text-white hover:text-sage-200 transition-colors">Dashboard</a>
                            <a href="/plants/list" class="text-white hover:text-sage-200 transition-colors">Plants</a>
                            <a href="/seeds/list" class="text-white hover:text-sage-200 transition-colors">Seeds</a>
                            <a href="/property" class="text-white hover:text-sage-200 transition-colors">Properties</a>
                            <a href="/seasons/list" class="text-white hover:text-sage-200 transition-colors">Seasons</a>
                            <a href="/season_plans" class="text-white hover:text-sage-200 transition-colors">Season Plans</a>
                            <a href="/wishlist/plants" class="text-white hover:text-sage-200 transition-colors">Wishlist</a>
                            {% if user_context.is_admin %}
                                <a href="/admin" class="text-white hover:text-sage-200 transition-colors">Admin</a>
                            {% endif %}
                        </div>

                        <!-- Theme Toggle -->
                        <button id="theme-toggle" type="button" class="text-white hover:text-sage-200 p-2 rounded-lg transition-colors">
                            <svg id="theme-toggle-dark-icon" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                            <svg id="theme-toggle-light-icon" class="w-5 h-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                        </button>

                        <!-- User Menu -->
                        <div class="relative">
                            <button id="user-menu-button" class="flex items-center space-x-2 text-white hover:text-sage-200 transition-colors">
                                <div class="w-8 h-8 bg-sage-600 rounded-full flex items-center justify-center text-sm font-medium relative">
                                    <!-- Plant-like avatar with face -->
                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2C8.13 2 5 5.13 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1s1-.45 1-1v-2.26c.*********** 1 .26s.69-.1 1-.26V17c0 .55.45 1 1 1s1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.87-3.13-7-7-7z"/>
                                        <!-- Eyes -->
                                        <circle cx="10" cy="8" r="1"/>
                                        <circle cx="14" cy="8" r="1"/>
                                        <!-- Smile -->
                                        <path d="M10 11c0 1.1.9 2 2 2s2-.9 2-2"/>
                                    </svg>
                                </div>
                                <span class="hidden lg:block">{{ user_context.username }}</span>
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                            <!-- User Dropdown Menu -->
                            <div id="user-menu" class="absolute right-0 top-full mt-2 w-64 bg-white dark:bg-sage-800 rounded-md shadow-lg py-1 z-50 hidden border border-sage-200 dark:border-sage-700">
                                <!-- User Info Section -->
                                <div class="px-4 py-3 border-b border-sage-200 dark:border-sage-700">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-sage-600 rounded-full flex items-center justify-center text-white font-semibold">
                                            <!-- Plant-like avatar with face -->
                                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 2C8.13 2 5 5.13 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1s1-.45 1-1v-2.26c.*********** 1 .26s.69-.1 1-.26V17c0 .55.45 1 1 1s1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.87-3.13-7-7-7z"/>
                                                <!-- Eyes -->
                                                <circle cx="10" cy="8" r="1"/>
                                                <circle cx="14" cy="8" r="1"/>
                                                <!-- Smile -->
                                                <path d="M10 11c0 1.1.9 2 2 2s2-.9 2-2"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-sage-900 dark:text-sage-100">{{ user_context.username }}</p>
                                            <p class="text-xs text-sage-500 dark:text-sage-400">{{ user_context.role|title }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Household Switcher -->
                                <div class="px-4 py-2 border-b border-sage-200 dark:border-sage-700">
                                    <p class="text-xs font-medium text-sage-500 dark:text-sage-400 uppercase tracking-wide mb-2">Current Household</p>
                                    {% if user_context.current_household_name %}
                                        <div class="flex items-center space-x-2 text-sm text-sage-700 dark:text-sage-200 mb-2">
                                            <svg class="w-4 h-4 text-primary-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-9 9a1 1 0 001.414 1.414L2 12.414V17a1 1 0 001 1h3a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h3a1 1 0 001-1v-4.586l.293.293a1 1 0 001.414-1.414l-9-9z"></path>
                                            </svg>
                                            <span class="font-medium">{{ user_context.current_household_name }}</span>
                                        </div>
                                    {% endif %}
                                    <a href="/households" class="flex items-center space-x-2 text-sm text-sage-600 dark:text-sage-300 hover:bg-sage-100 dark:hover:bg-sage-700 rounded px-2 py-1 transition-colors">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>Switch Household</span>
                                    </a>
                                </div>

                                <!-- Menu Items -->
                                <div class="py-1">
                                    <a href="/profile" class="block px-4 py-2 text-sm text-sage-700 dark:text-sage-200 hover:bg-sage-100 dark:hover:bg-sage-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                        </svg>
                                        Profile Settings
                                    </a>
                                    <a href="/settings" class="block px-4 py-2 text-sm text-sage-700 dark:text-sage-200 hover:bg-sage-100 dark:hover:bg-sage-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                                        </svg>
                                        Account Settings
                                    </a>
                                    <a href="/households" class="block px-4 py-2 text-sm text-sage-700 dark:text-sage-200 hover:bg-sage-100 dark:hover:bg-sage-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                                        </svg>
                                        Manage Households
                                    </a>
                                </div>

                                <hr class="my-1 border-sage-200 dark:border-sage-700">

                                <div class="py-1">
                                    <a href="/auth/logout" class="block px-4 py-2 text-sm text-sage-700 dark:text-sage-200 hover:bg-sage-100 dark:hover:bg-sage-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd"></path>
                                        </svg>
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- Guest Navigation -->
                        <div class="flex items-center space-x-4">
                            <!-- Theme Toggle for guests -->
                            <button id="theme-toggle" type="button" class="text-white hover:text-sage-200 p-2 rounded-lg transition-colors">
                                <svg id="theme-toggle-dark-icon" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                                </svg>
                                <svg id="theme-toggle-light-icon" class="w-5 h-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                            <a href="/auth/register" class="text-white hover:text-sage-200 transition-colors">Register</a>
                            <a href="/auth/login" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded-md transition-colors">Login</a>
                        </div>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" type="button" class="text-white hover:text-sage-200 p-2">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation Menu -->
            <div id="mobile-menu" class="md:hidden hidden">
                <div class="px-2 pt-2 pb-3 space-y-1 border-t border-sage-700">
                    {% if user_context.is_authenticated %}
                        <a href="/" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Dashboard</a>
                        <a href="/plants/list" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Plants</a>
                        <a href="/seeds/list" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Seeds</a>
                        <a href="/property" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Properties</a>
                        <a href="/seasons/list" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Seasons</a>
                        <a href="/season_plans" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Season Plans</a>
                        <a href="/wishlist/plants" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Wishlist</a>
                        {% if user_context.is_admin %}
                            <a href="/admin" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Admin</a>
                        {% endif %}
                        <hr class="my-2 border-sage-700">
                        <a href="/profile" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Profile</a>
                        <a href="/settings" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Settings</a>
                        <a href="/households" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Households</a>
                        <a href="/auth/logout" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Logout</a>
                    {% else %}
                        <a href="/auth/register" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Register</a>
                        <a href="/auth/login" class="block px-3 py-2 text-white hover:text-sage-200 transition-colors">Login</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>
<main class="container mx-auto px-4 py-6">
    {% block content %}{% endblock %}
</main>

<script>
// Enhanced navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    // User menu dropdown functionality
    const userMenuButton = document.getElementById('user-menu-button');
    const userMenu = document.getElementById('user-menu');

    if (userMenuButton && userMenu) {
        userMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });

        // Close menu when clicking outside
        document.addEventListener('click', function() {
            userMenu.classList.add('hidden');
        });

        // Prevent menu from closing when clicking inside it
        userMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Mobile menu functionality
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function() {
            mobileMenu.classList.add('hidden');
        });

        // Prevent mobile menu from closing when clicking inside it
        mobileMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Close menus when window is resized to desktop size
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 768) { // md breakpoint
            if (mobileMenu) {
                mobileMenu.classList.add('hidden');
            }
        }
    });

    // Theme toggle is now handled by scripts.js - remove duplicate implementation
});
</script>
</body>
</html>
