{% extends "base.html" %}
{% block title %}Property View{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto bg-white dark:bg-gray-800 p-6 rounded shadow">
    <h2 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Property: {{ property.name }}</h2>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded">
            <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Property Details</h3>
            <p class="text-gray-800 dark:text-gray-200"><strong>Inside Area:</strong> {{ property.inside_area }} m²</p>
            <p class="text-gray-800 dark:text-gray-200"><strong>Outside Area:</strong> {{ property.outside_area }} m²</p>
            <p class="text-gray-800 dark:text-gray-200"><strong>Floors:</strong> {{ property.floors }}</p>
        </div>

        <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded">
            <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Growing Areas</h3>
            <p class="text-gray-800 dark:text-gray-200"><strong>Total Growing Area:</strong> {{ total_growing_area }} m²</p>
            <p class="text-gray-800 dark:text-gray-200"><strong>Outside Growing Area:</strong> {{ outside_growing_area }} m²</p>
            <p class="text-gray-800 dark:text-gray-200"><strong>Inside Growing Area:</strong> {{ inside_growing_area }} m²</p>
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Floor Selector</h3>
        <div class="flex flex-wrap gap-2">
            <a href="?floor=-1" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 {% if current_floor == -1 %}opacity-50 cursor-not-allowed{% endif %}">
                Outside
            </a>
            {% for i in range(end=property.floors) %}
            <a href="?floor={{ i }}" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 {% if current_floor == i %}opacity-50 cursor-not-allowed{% endif %}">
                Floor {{ i }}
            </a>
            {% endfor %}
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">{{ current_floor_name }} Layout</h3>
        <div class="relative w-full h-96 bg-white dark:bg-gray-900 rounded shadow border border-gray-300 dark:border-gray-600">
            <canvas id="viewCanvas" class="w-full h-full border border-gray-300 dark:border-gray-600 rounded"></canvas>
            <div class="absolute top-2 right-2 bg-white dark:bg-gray-800 p-2 rounded shadow text-sm">
                <div class="text-gray-900 dark:text-white">Floor: <span class="font-bold">{{ current_floor_name }}</span></div>
            </div>
        </div>
    </div>

    <div class="mt-6">
        <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Growing Areas on {{ current_floor_name }}</h3>
        {% if growing_areas %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for area in growing_areas %}
            <div class="bg-green-100 dark:bg-green-900 p-4 rounded border border-green-200 dark:border-green-700">
                <h4 class="font-medium text-green-900 dark:text-green-100">{{ area.shape_type }}</h4>
                <p class="text-green-800 dark:text-green-200">Area: {{ area.area|default(value="N/A") }} m²</p>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <p class="text-gray-500 dark:text-gray-400">No growing areas defined for this floor.</p>
        {% endif %}
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const canvas = document.getElementById('viewCanvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size to match container size
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
            drawLayout();
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        function drawLayout() {
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            try {
                // Draw property boundaries
                const propertyShapes = {{ property_shapes|safe }};
                console.log('Property shapes:', propertyShapes);
                if (Array.isArray(propertyShapes) && propertyShapes.length > 0) {
                    propertyShapes.forEach(shape => {
                        drawShape(shape, 'rgba(128, 128, 128, 0.2)', 'rgba(128, 128, 128, 0.8)');
                    });
                } else {
                    console.log('No property shapes found');
                }

                // Draw growing areas
                const growingAreas = {{ growing_areas_json|safe }};
                console.log('Growing areas:', growingAreas);
                if (Array.isArray(growingAreas) && growingAreas.length > 0) {
                    growingAreas.forEach(shape => {
                        drawShape(shape, 'rgba(76, 175, 80, 0.3)', 'rgba(76, 175, 80, 0.8)');
                    });
                } else {
                    console.log('No growing areas found');
                }

                // If no shapes, show a helpful message
                if ((!propertyShapes || propertyShapes.length === 0) &&
                    (!growingAreas || growingAreas.length === 0)) {
                    ctx.fillStyle = 'rgba(128, 128, 128, 0.7)';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('No property layout available for this floor', canvas.width / 2, canvas.height / 2 - 10);
                    ctx.fillText('Use the Property Wizard to create a layout', canvas.width / 2, canvas.height / 2 + 10);
                }
            } catch (error) {
                console.error('Error drawing layout:', error);
                // Draw a placeholder message
                ctx.fillStyle = 'rgba(255, 0, 0, 0.7)';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Error loading property layout', canvas.width / 2, canvas.height / 2);
            }
        }

        function drawShape(shape, fillColor, strokeColor) {
            try {
                let points = [];

                // Handle different shape data formats
                if (shape.points) {
                    points = shape.points;
                } else if (shape.shape_data) {
                    if (typeof shape.shape_data === 'string') {
                        points = JSON.parse(shape.shape_data);
                    } else {
                        points = shape.shape_data;
                    }
                }

                if (!Array.isArray(points) || points.length === 0) {
                    console.warn('Invalid points data for shape:', shape);
                    return;
                }

                ctx.beginPath();
                ctx.fillStyle = fillColor;
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = 2;

                // Draw as polygon/freehand by default
                ctx.moveTo(points[0].x, points[0].y);
                for (let i = 1; i < points.length; i++) {
                    ctx.lineTo(points[i].x, points[i].y);
                }
                ctx.closePath();

                ctx.fill();
                ctx.stroke();
            } catch (error) {
                console.error('Error drawing shape:', error, shape);
            }
        }
    });
</script>
{% endblock %}
