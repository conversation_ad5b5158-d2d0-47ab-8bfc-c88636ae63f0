{% extends "base.html" %}
{% block title %}Home - Garden Planner{% endblock %}
{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Hero Section -->
    <div class="text-center py-12">
        <h1 class="text-5xl font-bold text-sage-900 dark:text-sage-100 mb-6">
            Welcome to Garden Planner{% if user_context.username %}, {{ user_context.username }}{% endif %}! 🌱
        </h1>
        <p class="text-xl text-sage-600 dark:text-sage-300 mb-8 max-w-3xl mx-auto">
            Plan, manage, and optimize your garden with our comprehensive gardening tools.
            Track plants, manage seasons, and get personalized notifications for your garden care.
        </p>

        {% if not user_context.is_authenticated %}
            <div class="space-x-4">
                <a href="/auth/register" class="bg-sage-600 hover:bg-sage-700 text-white px-8 py-3 rounded-lg text-lg font-medium transition-colors">
                    Get Started
                </a>
                <a href="/auth/login" class="border border-sage-600 text-sage-600 hover:bg-sage-50 dark:hover:bg-sage-900 px-8 py-3 rounded-lg text-lg font-medium transition-colors">
                    Login
                </a>
            </div>
        {% endif %}
    </div>

    {% if user_context.is_authenticated %}
        <!-- Quick Actions for Authenticated Users -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-sage-100 dark:bg-sage-900 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-sage-600 dark:text-sage-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100">Manage Plants</h3>
                </div>
                <p class="text-sage-600 dark:text-sage-300 mb-4">Add and manage your plant database with detailed information.</p>
                <a href="/plants/list" class="text-sage-700 hover:text-sage-900 dark:text-sage-300 dark:hover:text-sage-100 font-semibold">View Plants →</a>
            </div>

            <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-sage-200 dark:bg-sage-700 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-sage-700 dark:text-sage-300" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100">Properties</h3>
                </div>
                <p class="text-sage-600 dark:text-sage-300 mb-4">Design and manage your garden properties and growing areas.</p>
                <a href="/property" class="text-sage-700 hover:text-sage-900 dark:text-sage-300 dark:hover:text-sage-100 font-semibold">View Properties →</a>
            </div>

            <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-sage-300 dark:bg-sage-600 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-sage-800 dark:text-sage-200" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100">Season Plans</h3>
                </div>
                <p class="text-sage-600 dark:text-sage-300 mb-4">Plan your growing seasons and optimize crop rotations.</p>
                <a href="/season_plans" class="text-sage-700 hover:text-sage-900 dark:text-sage-300 dark:hover:text-sage-100 font-semibold">View Plans →</a>
            </div>

            <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-sage-400 dark:bg-sage-500 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-sage-900 dark:text-sage-100" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100">Seeds & Inventory</h3>
                </div>
                <p class="text-sage-600 dark:text-sage-300 mb-4">Track your seed inventory and planting schedules.</p>
                <a href="/seeds/list" class="text-sage-700 hover:text-sage-900 dark:text-sage-300 dark:hover:text-sage-100 font-semibold">View Seeds →</a>
            </div>

            <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-sage-500 dark:bg-sage-400 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-sage-100 dark:text-sage-900" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM8 15v-3a1 1 0 011-1h2a1 1 0 011 1v3H8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100">Households</h3>
                </div>
                <p class="text-sage-600 dark:text-sage-300 mb-4">Manage and share your garden with household members.</p>
                <a href="/households" class="text-sage-700 hover:text-sage-900 dark:text-sage-300 dark:hover:text-sage-100 font-semibold">View Households →</a>
            </div>

            <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-sage-600 dark:bg-sage-300 rounded-lg flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-sage-100 dark:text-sage-800" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100">Notifications</h3>
                </div>
                <p class="text-sage-600 dark:text-sage-300 mb-4">Stay on top of watering, fertilizing, and care schedules.</p>
                <a href="/notifications/list" class="text-sage-700 hover:text-sage-900 dark:text-sage-300 dark:hover:text-sage-100 font-semibold">View Notifications →</a>
            </div>
        </div>
    {% else %}
        <!-- Features for Non-Authenticated Users -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div class="text-center">
                <div class="w-16 h-16 bg-sage-100 dark:bg-sage-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-sage-600 dark:text-sage-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-2">Plant Database</h3>
                <p class="text-sage-600 dark:text-sage-300">Comprehensive plant information with growing requirements and care instructions.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-sage-200 dark:bg-sage-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-sage-700 dark:text-sage-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-2">Season Planning</h3>
                <p class="text-sage-600 dark:text-sage-300">Plan your garden seasons with automated optimization and crop rotation suggestions.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-sage-300 dark:bg-sage-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-sage-800 dark:text-sage-200" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-2">Smart Notifications</h3>
                <p class="text-sage-600 dark:text-sage-300">Get personalized reminders for watering, fertilizing, and seasonal garden tasks.</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
