{% extends "base.html" %}

{% block title %}{{ plan.name }} - Season Plan{% endblock %}

{% block head %}
<!-- Three.js for 3D visualization -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
<script src="/static/js/property-visualizer.js"></script>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">{{ plan.name }}</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-1">
                {{ plan.start_date }} to {{ plan.end_date }}
                {% if plan.property %} • {{ plan.property.name }}{% endif %}
            </p>
        </div>
        <div class="space-x-2">
            <a href="/season_plans/{{ plan.id }}/edit" class="bg-sage-600 hover:bg-sage-700 text-white font-medium py-2 px-4 rounded transition-colors">
                Edit Plan
            </a>
            <form method="post" action="/season_plans/{{ plan.id }}/delete" class="inline">
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded transition-colors"
                    onclick="return confirm('Are you sure you want to delete this plan?')">
                    Delete Plan
                </button>
            </form>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Plan Details -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow-md rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Plan Details</h2>

                <div class="mb-4">
                    <p class="text-sm text-gray-500">Season</p>
                    <p class="text-md font-medium">{{ plan.season_name }}</p>
                </div>

                <div class="mb-4">
                    <p class="text-sm text-gray-500">Property</p>
                    <p class="text-md font-medium">{{ plan.property_name }}</p>
                </div>

                {% if plan.growing_area %}
                <div class="mb-4">
                    <p class="text-sm text-gray-500">Growing Area</p>
                    <p class="text-md font-medium">
                        {% if plan.growing_area.name %}
                            {{ plan.growing_area.name }}
                        {% else %}
                            Area {{ plan.growing_area.id }}
                        {% endif %}
                    </p>
                </div>
                {% endif %}

                <div class="mb-4">
                    <p class="text-sm text-gray-500">Date Range</p>
                    <p class="text-md font-medium">{{ plan.start_date }} to {{ plan.end_date }}</p>
                </div>

                {% if plan.description %}
                <div class="mb-4">
                    <p class="text-sm text-gray-500">Description</p>
                    <p class="text-md">{{ plan.description }}</p>
                </div>
                {% endif %}
            </div>

            <!-- Add Plant Form -->
            <div class="bg-white shadow-md rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Add Plant to Plan</h2>

                <form method="post" action="/season_plans/{{ plan.id }}/add_plant">
                    <div class="mb-4">
                        <label for="plant_id" class="block text-sm font-medium text-gray-700 mb-1">Plant</label>
                        <select id="plant_id" name="plant_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                            <option value="">Select a plant</option>
                            {% for plant in plan.available_plants %}
                            <option value="{{ plant.id }}">{{ plant.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                        <input type="number" id="quantity" name="quantity" min="1" value="1" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                    </div>

                    {% if plan.growing_area %}
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="position_x" class="block text-sm font-medium text-gray-700 mb-1">Position X</label>
                            <input type="number" id="position_x" name="position_x"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                        </div>
                        <div>
                            <label for="position_y" class="block text-sm font-medium text-gray-700 mb-1">Position Y</label>
                            <input type="number" id="position_y" name="position_y"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                        </div>
                    </div>
                    {% endif %}

                    <div class="mb-4">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes (Optional)</label>
                        <textarea id="notes" name="notes" rows="2"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"></textarea>
                    </div>

                    <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Add Plant
                    </button>
                </form>
            </div>
        </div>

        <!-- Plants List -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow-md rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Plants in this Plan</h2>

                {% if plan.plants %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plant</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                {% if plan.growing_area %}
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                                {% endif %}
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for plant in plan.plants %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ plant.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ plant.quantity }}</div>
                                </td>
                                {% if plan.growing_area %}
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if plant.position_x is not none and plant.position_y is not none %}
                                    <div class="text-sm text-gray-900">({{ plant.position_x }}, {{ plant.position_y }})</div>
                                    {% else %}
                                    <div class="text-sm text-gray-500">Not specified</div>
                                    {% endif %}
                                </td>
                                {% endif %}
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">{% if plant.notes %}{{ plant.notes }}{% endif %}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="/season_plans/{{ plan.id }}/edit_plant/{{ plant.id }}" class="text-green-600 hover:text-green-900 mr-3">Edit</a>
                                    <form method="post" action="/season_plans/{{ plan.id }}/remove_plant/{{ plant.id }}" class="inline">
                                        <button type="submit" class="text-red-600 hover:text-red-900"
                                            onclick="return confirm('Are you sure you want to remove this plant?')">
                                            Remove
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <p class="text-gray-500">No plants have been added to this plan yet.</p>
                </div>
                {% endif %}
            </div>

            {% if plan.growing_area %}
            <!-- Garden Layout -->
            <div class="bg-white shadow-md rounded-lg p-6 mt-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Garden Layout</h2>

                <div id="garden-layout" class="border border-gray-300 rounded-md" style="height: 400px; position: relative;">
                    <!-- Plants will be positioned here via JavaScript -->
                    {% for plant in plan.plants %}
                        {% if plant.position_x is not none and plant.position_y is not none %}
                        <div class="plant-marker"
                             data-plant-id="{{ plant.id }}"
                             data-plant-name="{{ plant.name }}"
                             data-position-x="{{ plant.position_x }}"
                             data-position-y="{{ plant.position_y }}"
                             data-quantity="{{ plant.quantity }}"
                             style="position: absolute; left: {{ plant.position_x }}%; top: {{ plant.position_y }}%;">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold cursor-pointer"
                                 title="{{ plant.name }} ({{ plant.quantity }})">
                                {{ plant.name | truncate(length=1) }}
                            </div>
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>

                <div class="mt-2 text-sm text-gray-500">
                    <p>Click on a plant marker to see details.</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% if plan.growing_area %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Plant marker click handler
        const plantMarkers = document.querySelectorAll('.plant-marker');
        plantMarkers.forEach(marker => {
            marker.addEventListener('click', function() {
                const plantId = this.dataset.plantId;
                const plantName = this.dataset.plantName;
                const quantity = this.dataset.quantity;

                alert(`Plant: ${plantName}\nQuantity: ${quantity}`);
                // You could replace this with a modal or more sophisticated UI
            });
        });
    });
</script>
{% endif %}
{% endblock %}
